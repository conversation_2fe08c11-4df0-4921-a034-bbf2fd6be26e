#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一CSV特征文件名格式
解决标注文件中的视频名称与实际特征文件名不匹配的问题
"""

import os
import json
import shutil
from pathlib import Path
from collections import defaultdict

def load_annotation_files():
    """加载所有标注文件，获取视频名称列表"""
    annotation_files = [
        "../../../data/MultiClassTAD/multiclass_tad_train.json",
        "../../../data/MultiClassTAD/multiclass_tad_val.json"
    ]
    
    video_names = set()
    
    for ann_file in annotation_files:
        if os.path.exists(ann_file):
            print(f"📖 加载标注文件: {ann_file}")
            with open(ann_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                video_names.update(data.keys())
            print(f"   找到 {len(data)} 个视频")
        else:
            print(f"⚠️  标注文件不存在: {ann_file}")
    
    print(f"📊 总共找到 {len(video_names)} 个唯一视频名称")
    return video_names

def scan_csv_files(features_dir):
    """扫描所有CSV特征文件"""
    features_path = Path(features_dir)
    csv_files = list(features_path.rglob("*.csv"))
    
    print(f"📁 在 {features_dir} 中找到 {len(csv_files)} 个CSV文件")
    
    # 按子目录分组
    by_subdir = defaultdict(list)
    for csv_file in csv_files:
        subdir = csv_file.parent.name
        by_subdir[subdir].append(csv_file)
    
    for subdir, files in by_subdir.items():
        print(f"   {subdir}: {len(files)} 个文件")
    
    return csv_files

def create_filename_mapping(video_names, csv_files):
    """创建文件名映射关系"""
    mapping = {}
    unmatched_videos = []
    unmatched_files = []
    
    print("\n🔍 分析文件名匹配关系...")
    
    # 为每个视频名称查找对应的CSV文件
    for video_name in video_names:
        matched_file = None
        
        # 尝试不同的文件名变体
        possible_names = [
            video_name + ".csv",
            video_name + ".mp4.csv",
        ]
        
        # 如果视频名称不以_roi结尾，也尝试添加_roi后缀
        if not video_name.endswith('_roi'):
            possible_names.extend([
                video_name + "_roi.csv",
                video_name + "_roi.mp4.csv",
            ])
        
        # 如果视频名称以_roi结尾，也尝试移除_roi后缀
        if video_name.endswith('_roi'):
            base_name = video_name[:-4]  # 移除'_roi'
            possible_names.extend([
                base_name + ".csv",
                base_name + ".mp4.csv",
            ])
        
        # 在所有CSV文件中查找匹配
        for csv_file in csv_files:
            csv_filename = csv_file.name
            if csv_filename in possible_names:
                matched_file = csv_file
                break
        
        if matched_file:
            # 目标文件名：视频名称 + .csv
            target_name = video_name + ".csv"
            if matched_file.name != target_name:
                mapping[str(matched_file)] = target_name
        else:
            unmatched_videos.append(video_name)
    
    # 找出没有匹配的CSV文件
    matched_files = set(str(Path(k).resolve()) for k in mapping.keys())
    for csv_file in csv_files:
        if str(csv_file.resolve()) not in matched_files:
            # 检查是否已经是正确格式
            stem = csv_file.stem
            if stem.endswith('.mp4'):
                stem = stem[:-4]  # 移除.mp4
            
            expected_name = stem + ".csv"
            if csv_file.name != expected_name and stem not in video_names:
                unmatched_files.append(str(csv_file))
    
    print(f"✅ 需要重命名的文件: {len(mapping)} 个")
    print(f"⚠️  未匹配的视频: {len(unmatched_videos)} 个")
    print(f"⚠️  未匹配的文件: {len(unmatched_files)} 个")
    
    return mapping, unmatched_videos, unmatched_files

def preview_changes(mapping):
    """预览将要进行的更改"""
    if not mapping:
        print("📋 没有需要重命名的文件")
        return
    
    print(f"\n📋 预览重命名操作 ({len(mapping)} 个文件):")
    print("-" * 80)
    
    for i, (old_path, new_name) in enumerate(mapping.items(), 1):
        old_file = Path(old_path)
        new_path = old_file.parent / new_name
        
        print(f"{i:3d}. {old_file.name}")
        print(f"     -> {new_name}")
        print(f"     路径: {old_file.parent}")
        
        if i >= 10:  # 只显示前10个
            remaining = len(mapping) - 10
            if remaining > 0:
                print(f"     ... 还有 {remaining} 个文件")
            break
        print()

def apply_changes(mapping, dry_run=True):
    """应用文件重命名"""
    if not mapping:
        print("📋 没有需要重命名的文件")
        return True
    
    success_count = 0
    error_count = 0
    
    action = "预演" if dry_run else "执行"
    print(f"\n🚀 {action}重命名操作...")
    
    for old_path, new_name in mapping.items():
        old_file = Path(old_path)
        new_path = old_file.parent / new_name
        
        try:
            if dry_run:
                # 只检查是否可以重命名
                if old_file.exists():
                    if new_path.exists():
                        print(f"❌ 目标文件已存在: {new_path}")
                        error_count += 1
                    else:
                        print(f"✅ 可以重命名: {old_file.name} -> {new_name}")
                        success_count += 1
                else:
                    print(f"❌ 源文件不存在: {old_file}")
                    error_count += 1
            else:
                # 实际执行重命名
                if new_path.exists():
                    print(f"❌ 目标文件已存在，跳过: {new_path}")
                    error_count += 1
                else:
                    old_file.rename(new_path)
                    print(f"✅ 重命名成功: {old_file.name} -> {new_name}")
                    success_count += 1
                    
        except Exception as e:
            print(f"❌ 重命名失败: {old_file.name} -> {new_name}")
            print(f"   错误: {e}")
            error_count += 1
    
    print(f"\n📊 {action}结果:")
    print(f"   成功: {success_count} 个")
    print(f"   失败: {error_count} 个")
    
    return error_count == 0

def main():
    print("=" * 80)
    print("🔧 CSV特征文件名统一工具")
    print("=" * 80)
    
    # 配置
    features_dir = "../../../data/MultiClassTAD/features_slowonly"
    
    # 检查目录是否存在
    if not os.path.exists(features_dir):
        print(f"❌ 特征目录不存在: {features_dir}")
        return
    
    # 1. 加载标注文件
    video_names = load_annotation_files()
    if not video_names:
        print("❌ 没有找到视频名称")
        return
    
    # 2. 扫描CSV文件
    csv_files = scan_csv_files(features_dir)
    if not csv_files:
        print("❌ 没有找到CSV文件")
        return
    
    # 3. 创建映射关系
    mapping, unmatched_videos, unmatched_files = create_filename_mapping(video_names, csv_files)
    
    # 4. 显示未匹配的项目
    if unmatched_videos:
        print(f"\n⚠️  未匹配的视频 ({len(unmatched_videos)} 个):")
        for video in unmatched_videos[:5]:  # 只显示前5个
            print(f"   {video}")
        if len(unmatched_videos) > 5:
            print(f"   ... 还有 {len(unmatched_videos) - 5} 个")
    
    if unmatched_files:
        print(f"\n⚠️  未匹配的文件 ({len(unmatched_files)} 个):")
        for file_path in unmatched_files[:5]:  # 只显示前5个
            print(f"   {Path(file_path).name}")
        if len(unmatched_files) > 5:
            print(f"   ... 还有 {len(unmatched_files) - 5} 个")
    
    # 5. 预览更改
    preview_changes(mapping)
    
    # 6. 询问是否执行
    if mapping:
        print("\n" + "=" * 80)
        choice = input("是否执行重命名操作？(y/N): ").strip().lower()
        
        if choice in ['y', 'yes']:
            # 先预演
            print("\n🧪 预演模式...")
            if apply_changes(mapping, dry_run=True):
                print("\n✅ 预演成功，开始实际执行...")
                apply_changes(mapping, dry_run=False)
                print("\n🎉 文件重命名完成！")
            else:
                print("\n❌ 预演发现问题，取消执行")
        else:
            print("\n⏹️  操作已取消")
    else:
        print("\n✅ 所有文件名已经是正确格式，无需修改")

if __name__ == '__main__':
    main()
