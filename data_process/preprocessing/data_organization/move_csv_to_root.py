#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将所有CSV特征文件从子目录迁移到根目录
简化文件路径结构，解决路径查找问题
"""

import os
import shutil
from pathlib import Path
from collections import defaultdict

def scan_csv_files(features_dir):
    """扫描所有CSV特征文件"""
    features_path = Path(features_dir)
    
    # 找到所有CSV文件
    all_csv_files = list(features_path.rglob("*.csv"))
    
    # 分类：根目录中的文件 vs 子目录中的文件
    root_files = []
    subdir_files = []
    
    for csv_file in all_csv_files:
        if csv_file.parent == features_path:
            root_files.append(csv_file)
        else:
            subdir_files.append(csv_file)
    
    print(f"📁 特征目录: {features_dir}")
    print(f"   总文件数: {len(all_csv_files)}")
    print(f"   根目录中: {len(root_files)} 个")
    print(f"   子目录中: {len(subdir_files)} 个")
    
    # 按子目录分组显示
    if subdir_files:
        by_subdir = defaultdict(list)
        for csv_file in subdir_files:
            subdir = csv_file.parent.name
            by_subdir[subdir].append(csv_file)
        
        print(f"\n📂 子目录分布:")
        for subdir, files in by_subdir.items():
            print(f"   {subdir}: {len(files)} 个文件")
    
    return root_files, subdir_files

def check_name_conflicts(root_files, subdir_files, target_dir):
    """检查文件名冲突"""
    conflicts = []
    existing_names = set()
    
    # 收集根目录中已有的文件名
    for root_file in root_files:
        existing_names.add(root_file.name)
    
    # 检查子目录文件是否与根目录文件冲突
    for subdir_file in subdir_files:
        if subdir_file.name in existing_names:
            conflicts.append((subdir_file, subdir_file.name))
        else:
            existing_names.add(subdir_file.name)
    
    if conflicts:
        print(f"\n⚠️  发现文件名冲突 ({len(conflicts)} 个):")
        for conflict_file, name in conflicts:
            print(f"   {name}")
            print(f"     源: {conflict_file}")
            print(f"     目标: {target_dir / name}")
    
    return conflicts

def preview_move_operations(subdir_files, target_dir):
    """预览移动操作"""
    if not subdir_files:
        print("📋 没有需要移动的文件")
        return
    
    print(f"\n📋 预览移动操作 ({len(subdir_files)} 个文件):")
    print("-" * 80)
    
    for i, csv_file in enumerate(subdir_files, 1):
        target_path = target_dir / csv_file.name
        
        print(f"{i:3d}. {csv_file.name}")
        print(f"     从: {csv_file.parent}")
        print(f"     到: {target_dir}")
        
        if i >= 10:  # 只显示前10个
            remaining = len(subdir_files) - 10
            if remaining > 0:
                print(f"     ... 还有 {remaining} 个文件")
            break
        print()

def move_files(subdir_files, target_dir, dry_run=True):
    """执行文件移动"""
    if not subdir_files:
        print("📋 没有需要移动的文件")
        return True
    
    success_count = 0
    error_count = 0
    
    action = "预演" if dry_run else "执行"
    print(f"\n🚀 {action}文件移动...")
    
    for csv_file in subdir_files:
        target_path = target_dir / csv_file.name
        
        try:
            if dry_run:
                # 只检查是否可以移动
                if csv_file.exists():
                    if target_path.exists():
                        print(f"❌ 目标文件已存在: {csv_file.name}")
                        error_count += 1
                    else:
                        print(f"✅ 可以移动: {csv_file.name}")
                        success_count += 1
                else:
                    print(f"❌ 源文件不存在: {csv_file}")
                    error_count += 1
            else:
                # 实际执行移动
                if target_path.exists():
                    print(f"❌ 目标文件已存在，跳过: {csv_file.name}")
                    error_count += 1
                else:
                    shutil.move(str(csv_file), str(target_path))
                    print(f"✅ 移动成功: {csv_file.name}")
                    success_count += 1
                    
        except Exception as e:
            print(f"❌ 移动失败: {csv_file.name}")
            print(f"   错误: {e}")
            error_count += 1
    
    print(f"\n📊 {action}结果:")
    print(f"   成功: {success_count} 个")
    print(f"   失败: {error_count} 个")
    
    return error_count == 0

def cleanup_empty_dirs(features_dir):
    """清理空的子目录"""
    features_path = Path(features_dir)
    
    empty_dirs = []
    for item in features_path.iterdir():
        if item.is_dir():
            # 检查目录是否为空
            try:
                if not any(item.iterdir()):
                    empty_dirs.append(item)
            except PermissionError:
                print(f"⚠️  无法访问目录: {item}")
    
    if empty_dirs:
        print(f"\n🗑️  发现空目录 ({len(empty_dirs)} 个):")
        for empty_dir in empty_dirs:
            print(f"   {empty_dir.name}")
        
        choice = input("\n是否删除空目录？(y/N): ").strip().lower()
        if choice in ['y', 'yes']:
            for empty_dir in empty_dirs:
                try:
                    empty_dir.rmdir()
                    print(f"✅ 删除空目录: {empty_dir.name}")
                except Exception as e:
                    print(f"❌ 删除失败: {empty_dir.name} - {e}")

def main():
    print("=" * 80)
    print("📦 CSV特征文件迁移工具")
    print("   将所有CSV文件从子目录移动到根目录")
    print("   注意：如果使用了更新后的postprocess_features.py，")
    print("   文件已经直接输出到根目录，可能不需要此工具")
    print("=" * 80)
    
    # 配置
    features_dir = "../../../data/MultiClassTAD/features_slowonly"
    
    # 检查目录是否存在
    if not os.path.exists(features_dir):
        print(f"❌ 特征目录不存在: {features_dir}")
        return
    
    features_path = Path(features_dir)
    
    # 1. 扫描CSV文件
    root_files, subdir_files = scan_csv_files(features_dir)
    
    if not subdir_files:
        print("\n✅ 所有CSV文件已经在根目录中，无需移动")
        return
    
    # 2. 检查文件名冲突
    conflicts = check_name_conflicts(root_files, subdir_files, features_path)
    
    if conflicts:
        print(f"\n❌ 存在文件名冲突，无法继续")
        print("请手动解决冲突后再运行此脚本")
        return
    
    # 3. 预览移动操作
    preview_move_operations(subdir_files, features_path)
    
    # 4. 询问是否执行
    print("\n" + "=" * 80)
    choice = input("是否执行文件移动操作？(y/N): ").strip().lower()
    
    if choice in ['y', 'yes']:
        # 先预演
        print("\n🧪 预演模式...")
        if move_files(subdir_files, features_path, dry_run=True):
            print("\n✅ 预演成功，开始实际执行...")
            if move_files(subdir_files, features_path, dry_run=False):
                print("\n🎉 文件移动完成！")
                
                # 5. 清理空目录
                cleanup_empty_dirs(features_dir)
                
                # 6. 验证结果
                print("\n🔍 验证移动结果...")
                final_root_files, final_subdir_files = scan_csv_files(features_dir)
                
                if not final_subdir_files:
                    print("✅ 所有CSV文件已成功移动到根目录")
                else:
                    print(f"⚠️  仍有 {len(final_subdir_files)} 个文件在子目录中")
            else:
                print("\n❌ 执行过程中发现问题")
        else:
            print("\n❌ 预演发现问题，取消执行")
    else:
        print("\n⏹️  操作已取消")

if __name__ == '__main__':
    main()
