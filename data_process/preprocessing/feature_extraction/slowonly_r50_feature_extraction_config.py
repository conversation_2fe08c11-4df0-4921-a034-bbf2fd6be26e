# Copyright (c) OpenMMLab. All rights reserved.
# SlowOnly R50 特征提取配置文件
# 基于Kinetics-400预训练模型，用于BMN时间动作定位特征提取

_base_ = [
    '../../../mmaction2/configs/_base_/models/slowonly_r50.py',
    '../../../mmaction2/configs/_base_/default_runtime.py'
]

# 模型设置 - 使用Kinetics-400预训练的SlowOnly R50
model = dict(
    type='Recognizer3D',
    backbone=dict(
        type='ResNet3dSlowOnly',
        depth=50,
        pretrained='https://download.openmmlab.com/mmaction/v1.0/recognition/slowonly/'
                   'slowonly_imagenet-pretrained-r50_8xb16-4x16x1-steplr-150e_kinetics400-'
                   'rgb/slowonly_imagenet-pretrained-r50_8xb16-4x16x1-steplr-150e_'
                   'kinetics400-rgb_20220901-e7b65fad.pth',
        lateral=False,
        conv1_kernel=(1, 7, 7),
        conv1_stride_t=1,
        pool1_stride_t=1,
        inflate=(0, 0, 1, 1),
        norm_eval=False),
    cls_head=dict(
        type='I3DHead',
        in_channels=2048,  # SlowOnly R50输出特征维度
        num_classes=400,   # Kinetics-400类别数
        spatial_type='avg',
        dropout_ratio=0.5,
        average_clips='prob'),
    data_preprocessor=dict(
        type='ActionDataPreprocessor',
        mean=[123.675, 116.28, 103.53],
        std=[58.395, 57.12, 57.375],
        format_shape='NCTHW'))

# 数据集设置
dataset_type = 'VideoDataset'
data_root = 'data'  # 修改为data根目录
ann_file = 'data/MultiClassTAD/video_list.txt'

file_client_args = dict(io_backend='disk')

# 测试数据管道 - 针对特征提取优化，减少内存使用
test_pipeline = [
    dict(type='DecordInit', **file_client_args),
    dict(
        type='SampleFrames',
        clip_len=8,           # SlowOnly标准clip长度
        frame_interval=8,     # 帧间隔
        num_clips=1,          # 单个clip
        test_mode=True),
    dict(type='DecordDecode'),
    dict(type='Resize', scale=(-1, 224)),  # 减小输入尺寸
    dict(type='CenterCrop', crop_size=224),
    dict(type='FormatShape', input_format='NCTHW'),
    dict(type='PackActionInputs')
]

# 测试数据加载器 - 减少内存使用
test_dataloader = dict(
    batch_size=1,
    num_workers=1,  # 减少worker数量
    persistent_workers=False,  # 关闭persistent workers
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=ann_file,
        data_prefix=dict(video=data_root),
        pipeline=test_pipeline,
        test_mode=True))

# 测试配置
test_cfg = dict(type='TestLoop')

# 默认钩子
default_hooks = dict(
    runtime_info=dict(type='RuntimeInfoHook'),
    timer=dict(type='IterTimerHook'),
    logger=dict(type='LoggerHook', interval=20, ignore_last=False),
    param_scheduler=dict(type='ParamSchedulerHook'),
    checkpoint=dict(type='CheckpointHook', interval=3, save_best='auto'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    sync_buffers=dict(type='SyncBuffersHook'))

# 环境设置
env_cfg = dict(
    cudnn_benchmark=False,
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0),
    dist_cfg=dict(backend='nccl'))

# 日志设置
vis_backends = [dict(type='LocalVisBackend')]
visualizer = dict(type='ActionVisualizer', vis_backends=vis_backends)
log_processor = dict(type='LogProcessor', window_size=20, by_epoch=True)
log_level = 'INFO'

# 加载设置
load_from = None
resume = False

# 工作目录
work_dir = './work_dirs/slowonly_feature_extraction'
