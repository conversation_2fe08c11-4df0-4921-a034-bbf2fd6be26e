#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特征形状是否符合BMN期望
"""

import sys
import os
import numpy as np

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 添加mmaction2到Python路径
mmaction2_path = os.path.join(current_dir, '..', 'mmaction2')
sys.path.insert(0, mmaction2_path)

def test_feature_loading():
    """测试特征加载和形状"""
    try:
        # 导入变换
        # 添加training/configs目录到Python路径以导入fix_transforms
        training_configs_path = os.path.join(current_dir, '..', '..', 'training', 'configs')
        if training_configs_path not in sys.path:
            sys.path.insert(0, training_configs_path)
        from fix_transforms import LoadLocalizationFeatureForBMN
        
        # 创建变换实例
        transform = LoadLocalizationFeatureForBMN()
        
        # 模拟数据输入
        test_file = '../../../data/MultiClassTAD/features_slowonly/20250727T075940Z_20250727T080440Z_decrypted-00.00.18.469-00.00.21.687-seg01_roi.csv'
        
        results = {
            'feature_path': test_file
        }
        
        print(f"🧪 测试特征加载:")
        print(f"   文件: {test_file}")
        
        # 执行变换
        output = transform.transform(results)
        
        if 'raw_feature' in output:
            feature = output['raw_feature']
            print(f"✅ 特征加载成功")
            print(f"   特征形状: {feature.shape}")
            print(f"   特征类型: {type(feature)}")
            print(f"   数据类型: {feature.dtype}")
            
            # 检查形状是否符合BMN期望
            if len(feature.shape) == 2:
                feat_dim, temporal_dim = feature.shape
                print(f"   特征维度: {feat_dim}")
                print(f"   时间维度: {temporal_dim}")
                
                if feat_dim == 2048:
                    print("✅ 特征维度正确 (2048)")
                else:
                    print(f"❌ 特征维度错误，期望2048，实际{feat_dim}")
                
                if temporal_dim == 100:
                    print("✅ 时间维度正确 (100)")
                else:
                    print(f"⚠️  时间维度为{temporal_dim}，可能因视频长度而异")
                
                print(f"\n📊 批处理后的期望形状: [batch_size, {feat_dim}, {temporal_dim}]")
                print(f"   这符合BMN期望的输入格式")
                
                return True
            else:
                print(f"❌ 特征形状错误，期望2D，实际{len(feature.shape)}D")
                return False
        else:
            print(f"❌ 输出中缺少 'raw_feature' 键")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_with_standard():
    """与标准LoadLocalizationFeature比较"""
    try:
        from mmaction.registry import TRANSFORMS
        
        # 测试标准变换
        standard_transform = TRANSFORMS.build(dict(type='LoadLocalizationFeature'))
        
        # 测试自定义变换
        from fix_transforms import LoadLocalizationFeatureForBMN
        custom_transform = LoadLocalizationFeatureForBMN()
        
        test_file = '../../../data/MultiClassTAD/features_slowonly/20250727T075940Z_20250727T080440Z_decrypted-00.00.18.469-00.00.21.687-seg01_roi.csv'
        
        results = {'feature_path': test_file}
        
        print(f"\n🔍 比较标准变换与自定义变换:")
        
        # 标准变换
        standard_output = standard_transform.transform(results.copy())
        standard_feature = standard_output['raw_feature']
        print(f"   标准变换输出形状: {standard_feature.shape}")
        
        # 自定义变换
        custom_output = custom_transform.transform(results.copy())
        custom_feature = custom_output['raw_feature']
        print(f"   自定义变换输出形状: {custom_feature.shape}")
        
        # 检查是否是转置关系
        if (standard_feature.shape[0] == custom_feature.shape[1] and 
            standard_feature.shape[1] == custom_feature.shape[0]):
            print("✅ 自定义变换是标准变换的转置，符合预期")
            
            # 验证数据是否一致
            if np.allclose(standard_feature, custom_feature.T):
                print("✅ 数据内容一致，只是维度顺序不同")
            else:
                print("⚠️  数据内容不一致")
        else:
            print("❌ 形状关系不符合预期")
        
        return True
        
    except Exception as e:
        print(f"❌ 比较测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("=" * 60)
    print("🧪 BMN特征形状测试")
    print("=" * 60)
    
    # 测试特征加载
    feature_ok = test_feature_loading()
    
    # 比较变换
    if feature_ok:
        compare_ok = compare_with_standard()
    else:
        compare_ok = False
    
    print("\n" + "=" * 60)
    if feature_ok and compare_ok:
        print("🎉 所有测试通过! 特征形状符合BMN期望")
    else:
        print("❌ 测试失败，需要进一步修复")
    print("=" * 60)
