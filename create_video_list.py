#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为BMN特征提取创建视频列表文件
"""

import os
from pathlib import Path

def create_video_list():
    """创建视频列表文件"""
    video_dir = Path('/home/<USER>/johnny_ws/mmaction2_ws/data/raw_videos')
    output_file = Path('/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD/video_list.txt')
    
    # 确保输出目录存在
    output_file.parent.mkdir(parents=True, exist_ok=True)
    
    # 获取所有视频文件
    video_files = []
    for ext in ['*.mp4', '*.avi', '*.mov', '*.mkv']:
        video_files.extend(video_dir.glob(ext))
    
    # 写入视频列表文件
    with open(output_file, 'w') as f:
        for video_file in sorted(video_files):
            # 格式: 相对路径 标签(这里用0作为占位符)
            relative_path = f"raw_videos/{video_file.name}"
            f.write(f"{relative_path} 0\n")
    
    print(f"创建视频列表文件: {output_file}")
    print(f"包含 {len(video_files)} 个视频文件")
    
    return str(output_file)

if __name__ == "__main__":
    create_video_list()
